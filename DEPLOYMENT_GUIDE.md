# Course Manager AWS ECS Deployment Guide

## Overview

This guide walks you through deploying the Course Manager application to AWS ECS using Terraform. The deployment creates a production-ready infrastructure with high availability, auto-scaling, and monitoring.

## Architecture Components

- **VPC**: Custom VPC with public/private subnets across 2 AZs
- **RDS MySQL**: Managed database in private subnets with automated backups
- **ECS Fargate**: Serverless container platform for running the application
- **Application Load Balancer**: Distributes traffic with SSL termination
- **ECR**: Private Docker registry for container images
- **Auto Scaling**: Automatic scaling based on CPU, memory, and request metrics
- **CloudWatch**: Centralized logging and monitoring
- **Secrets Manager**: Secure storage for database credentials

## Prerequisites

1. **AWS Account** with appropriate permissions
2. **AWS CLI** installed and configured
3. **Terraform** >= 1.0 installed
4. **Docker** installed for building images
5. **Course Manager application** with Dockerfile

## Step-by-Step Deployment

### Step 1: Prepare the Terraform Configuration

1. **Move the terraform folder** to your infrastructure repository:
   ```bash
   # From your grade-server-2 directory
   mv terraform /path/to/your/infrastructure-repo/
   cd /path/to/your/infrastructure-repo/terraform
   ```

2. **Configure variables**:
   ```bash
   cp terraform.tfvars.example terraform.tfvars
   vim terraform.tfvars
   ```

3. **Important variables to customize**:
   ```hcl
   # Change these values
   aws_region = "us-west-2"  # Your preferred region
   environment = "prod"      # or "staging", "dev"
   project_name = "gradeservice"
   
   # IMPORTANT: Change this password!
   db_password = "your-very-secure-password-here"
   
   # Optional: Configure domain and SSL
   domain_name = "gradeservice.yourdomain.com"
   certificate_arn = "arn:aws:acm:region:account:certificate/cert-id"
   ```

### Step 2: Deploy Infrastructure

1. **Initialize Terraform**:
   ```bash
   terraform init
   ```

2. **Review the deployment plan**:
   ```bash
   terraform plan
   ```

3. **Deploy the infrastructure**:
   ```bash
   terraform apply
   ```
   
   This will create:
   - VPC and networking components (~5 minutes)
   - RDS database (~10 minutes)
   - ECS cluster and ALB (~5 minutes)
   - ECR repository and other resources (~2 minutes)

### Step 3: Build and Deploy Application

1. **Get ECR repository URL**:
   ```bash
   ECR_REPO=$(terraform output -raw ecr_repository_url)
   echo $ECR_REPO
   ```

2. **Login to ECR**:
   ```bash
   aws ecr get-login-password --region us-west-2 | \
     docker login --username AWS --password-stdin $ECR_REPO
   ```

3. **Build and push Docker image**:
   ```bash
   # Navigate to your application directory
   cd /path/to/grade-server-2
   
   # Build the image
   docker build -t gradeservice:latest .
   
   # Tag for ECR
   docker tag gradeservice:latest $ECR_REPO:latest
   
   # Push to ECR
   docker push $ECR_REPO:latest
   ```

4. **Deploy to ECS**:
   ```bash
   # Back to terraform directory
   cd /path/to/terraform
   
   # Get cluster and service names
   CLUSTER_NAME=$(terraform output -raw ecs_cluster_name)
   SERVICE_NAME=$(terraform output -raw ecs_service_name)
   
   # Force new deployment
   aws ecs update-service \
     --cluster $CLUSTER_NAME \
     --service $SERVICE_NAME \
     --force-new-deployment
   ```

### Step 4: Verify Deployment

1. **Check application URL**:
   ```bash
   terraform output application_url
   ```

2. **Monitor deployment**:
   ```bash
   # Watch service status
   aws ecs describe-services \
     --cluster $CLUSTER_NAME \
     --services $SERVICE_NAME
   
   # View application logs
   aws logs tail /aws/ecs/course-manager/gradeservice --follow
   ```

3. **Test the application**:
   ```bash
   APP_URL=$(terraform output -raw application_url)
   curl -I $APP_URL
   ```

## Alternative: Automated Deployment

Use the provided deployment script for a streamlined process:

```bash
cd terraform
./deploy.sh
```

This script will:
- Check prerequisites
- Deploy infrastructure
- Build and push Docker image
- Update ECS service
- Display deployment summary

## Database Migration

After deployment, you'll need to migrate your database:

### Option 1: Direct Connection (Temporary)

1. **Temporarily allow public access** to RDS (for migration only):
   ```bash
   # In terraform/rds.tf, temporarily set:
   # publicly_accessible = true
   terraform apply
   ```

2. **Get database endpoint**:
   ```bash
   DB_ENDPOINT=$(terraform output -raw rds_endpoint)
   ```

3. **Load your database**:
   ```bash
   mysql -h $DB_ENDPOINT -u gradeservice_user -p gradeservice < /path/to/your/database/local_dump.sql
   ```

4. **Revert public access**:
   ```bash
   # Change back to publicly_accessible = false
   terraform apply
   ```

### Option 2: ECS Exec (Recommended)

1. **Connect to running container**:
   ```bash
   # Get task ARN
   TASK_ARN=$(aws ecs list-tasks --cluster $CLUSTER_NAME --service-name $SERVICE_NAME --query 'taskArns[0]' --output text)
   
   # Connect to container
   aws ecs execute-command \
     --cluster $CLUSTER_NAME \
     --task $TASK_ARN \
     --container gradeservice \
     --interactive \
     --command "/bin/bash"
   ```

2. **Inside the container, load database**:
   ```bash
   # Upload your SQL file to S3 first, then download in container
   aws s3 cp s3://your-bucket/local_dump.sql /tmp/
   mysql -h $DB_HOST -u $DB_USERNAME -p$DB_PASSWORD $DB_NAME < /tmp/local_dump.sql
   ```

## Monitoring and Maintenance

### CloudWatch Dashboards

Create custom dashboards to monitor:
- ECS service metrics (CPU, memory, task count)
- ALB metrics (request count, response times, errors)
- RDS metrics (connections, CPU, storage)

### Scaling

Adjust scaling parameters in `terraform.tfvars`:
```hcl
app_desired_count = 3    # Increase base capacity
app_max_capacity = 20    # Allow higher scaling
```

### Updates

For application updates:
1. Build new Docker image with version tag
2. Update `image_tag` in `terraform.tfvars`
3. Run `terraform apply`

## Cost Optimization

### Development Environment

For development/testing, use smaller resources:
```hcl
db_instance_class = "db.t3.micro"
app_cpu = 512
app_memory = 1024
app_desired_count = 1
```

### Production Environment

For production, consider:
```hcl
db_instance_class = "db.t3.small"  # or larger
app_cpu = 1024
app_memory = 2048
app_desired_count = 2
app_max_capacity = 10
```

## Security Best Practices

1. **Change default passwords** in `terraform.tfvars`
2. **Use SSL certificates** for HTTPS
3. **Enable VPC Flow Logs** for network monitoring
4. **Set up AWS Config** for compliance
5. **Configure AWS WAF** for application protection
6. **Use least privilege IAM policies**

## Troubleshooting

### Common Issues

1. **ECS tasks failing to start**:
   - Check CloudWatch logs: `/ecs/gradeservice`
   - Verify Docker image exists in ECR
   - Check security group rules

2. **Database connection issues**:
   - Verify RDS security group allows ECS access
   - Check database credentials in Secrets Manager
   - Ensure database is in same VPC

3. **Load balancer health checks failing**:
   - Verify health check path is correct
   - Check application startup time
   - Review target group settings

### Useful Commands

```bash
# Check ECS service status
aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME

# View recent logs
aws logs tail /aws/ecs/course-manager/gradeservice --since 1h

# Check target group health
aws elbv2 describe-target-health --target-group-arn $(terraform output -raw target_group_arn)

# Scale service manually
aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --desired-count 3
```

## Cleanup

To destroy all resources:

```bash
terraform destroy
```

**Warning**: This will delete all data including the RDS database. Ensure you have backups!

## Support

For deployment issues:
1. Check AWS CloudWatch logs
2. Review Terraform state and outputs
3. Verify AWS service limits
4. Check this guide for troubleshooting steps
