# Application Load Balancer for Course Manager

# Security Group for Course Manager ALB
resource "aws_security_group" "course_manager_alb" {
  name_prefix = "${var.project_name}-course-manager-alb-"
  vpc_id      = aws_vpc.course_manager_vpc.id

  ingress {
    description = "HTTP for Course Manager"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    description = "HTTPS for Course Manager"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    description = "All outbound traffic from Course Manager ALB"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${var.project_name}-course-manager-alb-security-group"
    Environment = var.environment
    Service     = "course-manager"
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Application Load Balancer for Course Manager
resource "aws_lb" "course_manager_alb" {
  name               = "${var.project_name}-course-manager-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.course_manager_alb.id]
  subnets            = aws_subnet.course_manager_public_subnets[*].id

  enable_deletion_protection = false

  access_logs {
    bucket  = aws_s3_bucket.course_manager_alb_logs.id
    prefix  = "course-manager-alb-logs"
    enabled = true
  }

  tags = {
    Name        = "${var.project_name}-course-manager-application-load-balancer"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# S3 Bucket for Course Manager ALB Access Logs
resource "aws_s3_bucket" "course_manager_alb_logs" {
  bucket        = "${var.project_name}-course-manager-alb-logs-${random_string.course_manager_bucket_suffix.result}"
  force_destroy = true

  tags = {
    Name        = "${var.project_name}-course-manager-alb-access-logs"
    Environment = var.environment
    Service     = "course-manager"
  }
}

resource "aws_s3_bucket_versioning" "course_manager_alb_logs_versioning" {
  bucket = aws_s3_bucket.course_manager_alb_logs.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "course_manager_alb_logs_encryption" {
  bucket = aws_s3_bucket.course_manager_alb_logs.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "course_manager_alb_logs_lifecycle" {
  bucket = aws_s3_bucket.course_manager_alb_logs.id

  rule {
    id     = "delete_old_course_manager_logs"
    status = "Enabled"

    expiration {
      days = 30
    }

    noncurrent_version_expiration {
      noncurrent_days = 7
    }
  }
}

# Random string for unique Course Manager bucket naming
resource "random_string" "course_manager_bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

# S3 Bucket Policy for Course Manager ALB Access Logs
resource "aws_s3_bucket_policy" "course_manager_alb_logs_policy" {
  bucket = aws_s3_bucket.course_manager_alb_logs.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::************:root" # ELB service account for us-west-2
        }
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.course_manager_alb_logs.arn}/course-manager-alb-logs/AWSLogs/${data.aws_caller_identity.current.account_id}/*"
      },
      {
        Effect = "Allow"
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        }
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.course_manager_alb_logs.arn}/course-manager-alb-logs/AWSLogs/${data.aws_caller_identity.current.account_id}/*"
        Condition = {
          StringEquals = {
            "s3:x-amz-acl" = "bucket-owner-full-control"
          }
        }
      },
      {
        Effect = "Allow"
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        }
        Action   = "s3:GetBucketAcl"
        Resource = aws_s3_bucket.course_manager_alb_logs.arn
      }
    ]
  })
}

# ALB Target Group for Course Manager
resource "aws_lb_target_group" "course_manager_target_group" {
  name        = "${var.project_name}-course-manager-target-group"
  port        = var.app_port
  protocol    = "HTTP"
  vpc_id      = aws_vpc.course_manager_vpc.id
  target_type = "ip"

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200"
    path                = var.health_check_path
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
  }

  tags = {
    Name        = "${var.project_name}-course-manager-alb-target-group"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# ALB Listener (HTTP) for Course Manager
resource "aws_lb_listener" "course_manager_http_listener" {
  load_balancer_arn = aws_lb.course_manager_alb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

# ALB Listener (HTTPS) for Course Manager - conditional based on certificate
resource "aws_lb_listener" "course_manager_https_listener" {
  count = var.certificate_arn != "" ? 1 : 0

  load_balancer_arn = aws_lb.course_manager_alb.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"
  certificate_arn   = var.certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.course_manager_target_group.arn
  }
}

# ALB Listener (HTTP only) for Course Manager - when no certificate is provided
resource "aws_lb_listener" "course_manager_http_only_listener" {
  count = var.certificate_arn == "" ? 1 : 0

  load_balancer_arn = aws_lb.course_manager_alb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.course_manager_target_group.arn
  }
}
