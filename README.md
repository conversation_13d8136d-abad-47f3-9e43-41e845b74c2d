# Course Manager ECS Deployment with Terraform

This Terraform configuration deploys the Course Manager application to AWS ECS with a complete production-ready infrastructure.

## Architecture

The deployment creates:

- **VPC** with public and private subnets across 2 AZs
- **RDS MySQL** database in private subnets
- **ECS Fargate** cluster and service
- **Application Load Balancer** with SSL termination
- **ECR** repository for Docker images
- **Auto Scaling** based on CPU, memory, and request count
- **CloudWatch** logging and monitoring
- **Secrets Manager** for database credentials
- **S3** bucket for ALB access logs

## Prerequisites

1. **AWS CLI** configured with appropriate permissions
2. **Terraform** >= 1.0 installed
3. **Docker** image built and pushed to ECR (see deployment steps)

## Required AWS Permissions

Your AWS user/role needs permissions for:
- VPC, EC2, and networking resources
- RDS database management
- ECS cluster and service management
- ECR repository management
- IAM role and policy management
- CloudWatch logs and monitoring
- Secrets Manager
- S3 bucket management
- Application Load Balancer

## Quick Start

### 1. Configure Variables

```bash
# Copy the example variables file
cp terraform.tfvars.example terraform.tfvars

# Edit the variables file with your specific values
vim terraform.tfvars
```

**Important**: Change the `db_password` to a secure password!

### 2. Initialize Terraform

```bash
terraform init
```

### 3. Plan the Deployment

```bash
terraform plan
```

### 4. Deploy Infrastructure

```bash
terraform apply
```

### 5. Build and Push Docker Image

After the infrastructure is created, you'll need to build and push your Docker image:

```bash
# Get the ECR repository URL from Terraform output
ECR_REPO=$(terraform output -raw ecr_repository_url)

# Login to ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin $ECR_REPO

# Build and tag your Docker image (from your application directory)
docker build -t gradeservice .
docker tag gradeservice:latest $ECR_REPO:latest

# Push the image
docker push $ECR_REPO:latest
```

### 6. Update ECS Service

After pushing the image, update the ECS service to use the new image:

```bash
# Force a new deployment
aws ecs update-service --cluster gradeservice-cluster --service gradeservice-service --force-new-deployment
```

## Configuration

### Database Configuration

The RDS MySQL database is configured with:
- Multi-AZ deployment for high availability
- Automated backups with 7-day retention
- Performance Insights enabled
- Enhanced monitoring
- Encryption at rest

### Auto Scaling

The ECS service automatically scales based on:
- **CPU utilization** (target: 70%)
- **Memory utilization** (target: 80%)
- **Request count per target** (target: 1000 requests/minute)

### Security

- All resources are deployed in private subnets (except ALB)
- Security groups restrict access to necessary ports only
- Database credentials stored in AWS Secrets Manager
- SSL/TLS termination at the load balancer
- VPC Flow Logs can be enabled for network monitoring

## Monitoring and Logging

### CloudWatch Logs
- Application logs: `/ecs/gradeservice`
- ECS Exec logs: `/ecs/gradeservice/exec`

### CloudWatch Alarms
- High CPU utilization (>80%)
- High memory utilization (>85%)
- Alerts sent to SNS topic

### Accessing Logs
```bash
# View application logs
aws logs tail /aws/ecs/course-manager/gradeservice --follow

# View specific log stream
aws logs describe-log-streams --log-group-name /aws/ecs/course-manager/gradeservice
```

## Maintenance

### Updating the Application

1. Build and push new Docker image with a new tag
2. Update the `image_tag` variable in `terraform.tfvars`
3. Run `terraform apply`

### Database Maintenance

- Automated backups run daily during the configured backup window
- Maintenance occurs during the configured maintenance window
- Database can be scaled vertically by changing `db_instance_class`

### Scaling

Adjust these variables in `terraform.tfvars`:
- `app_desired_count`: Number of running tasks
- `app_min_capacity`: Minimum tasks for auto scaling
- `app_max_capacity`: Maximum tasks for auto scaling
- `app_cpu` and `app_memory`: Task resource allocation

## Troubleshooting

### ECS Service Issues
```bash
# Check service status
aws ecs describe-services --cluster gradeservice-cluster --services gradeservice-service

# Check task status
aws ecs list-tasks --cluster gradeservice-cluster --service-name gradeservice-service
aws ecs describe-tasks --cluster gradeservice-cluster --tasks <task-arn>
```

### Database Connection Issues
```bash
# Test database connectivity from ECS task
aws ecs execute-command --cluster gradeservice-cluster --task <task-arn> --container gradeservice --interactive --command "/bin/bash"
```

### Load Balancer Issues
```bash
# Check target group health
aws elbv2 describe-target-health --target-group-arn <target-group-arn>
```

## Costs

Estimated monthly costs (us-west-2):
- **ECS Fargate** (2 tasks, 1 vCPU, 2GB): ~$30
- **RDS db.t3.micro**: ~$15
- **Application Load Balancer**: ~$20
- **NAT Gateway** (2 AZs): ~$60
- **Data transfer and storage**: Variable

**Total estimated**: ~$125/month (excluding data transfer)

## Security Considerations

1. **Change default passwords** in `terraform.tfvars`
2. **Enable VPC Flow Logs** for network monitoring
3. **Configure SSL certificate** for HTTPS
4. **Set up proper IAM policies** for least privilege access
5. **Enable AWS Config** for compliance monitoring
6. **Consider AWS WAF** for application protection

## Cleanup

To destroy all resources:

```bash
terraform destroy
```

**Warning**: This will delete all data including the RDS database. Make sure to backup any important data first.

## Support

For issues with this Terraform configuration, check:
1. AWS CloudWatch logs for application errors
2. Terraform state for resource status
3. AWS Console for service health
4. This README for common troubleshooting steps
