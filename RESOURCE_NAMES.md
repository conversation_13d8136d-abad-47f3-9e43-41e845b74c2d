# Course Manager Infrastructure Resource Names

This document lists all the AWS resources created by the Terraform configuration with their descriptive names that clearly indicate they belong to the Course Manager infrastructure.

## VPC and Networking Resources

### VPC
- **Resource**: `aws_vpc.course_manager_vpc`
- **Name**: `${var.project_name}-course-manager-vpc`
- **Description**: Main VPC for Course Manager infrastructure

### Internet Gateway
- **Resource**: `aws_internet_gateway.course_manager_internet_gateway`
- **Name**: `${var.project_name}-course-manager-internet-gateway`
- **Description**: Internet gateway for Course Manager VPC

### Subnets
- **Public Subnets**: `aws_subnet.course_manager_public_subnets`
- **Name**: `${var.project_name}-course-manager-public-subnet-{1,2}`
- **Private Subnets**: `aws_subnet.course_manager_private_subnets`
- **Name**: `${var.project_name}-course-manager-private-subnet-{1,2}`

### NAT Gateways
- **EIPs**: `aws_eip.course_manager_nat_gateway_eips`
- **Name**: `${var.project_name}-course-manager-nat-gateway-eip-{1,2}`
- **NAT Gateways**: `aws_nat_gateway.course_manager_nat_gateways`
- **Name**: `${var.project_name}-course-manager-nat-gateway-{1,2}`

### Route Tables
- **Public Route Table**: `aws_route_table.course_manager_public_route_table`
- **Name**: `${var.project_name}-course-manager-public-route-table`
- **Private Route Tables**: `aws_route_table.course_manager_private_route_tables`
- **Name**: `${var.project_name}-course-manager-private-route-table-{1,2}`

## Database Resources

### RDS Instance
- **Resource**: `aws_db_instance.course_manager_database`
- **Name**: `${var.project_name}-course-manager-database`
- **Description**: MySQL database for Course Manager

### DB Subnet Group
- **Resource**: `aws_db_subnet_group.course_manager_db_subnet_group`
- **Name**: `${var.project_name}-course-manager-db-subnet-group`

### DB Parameter Group
- **Resource**: `aws_db_parameter_group.course_manager_db_parameter_group`
- **Name**: `${var.project_name}-course-manager-db-parameter-group`

### Database Security Group
- **Resource**: `aws_security_group.course_manager_database_security_group`
- **Name**: `${var.project_name}-course-manager-database-security-group`

### Database Credentials
- **Secret**: `aws_secretsmanager_secret.course_manager_db_credentials`
- **Name**: `${var.project_name}/course-manager/database/credentials`
- **Secret Version**: `aws_secretsmanager_secret_version.course_manager_db_credentials_version`

### RDS Monitoring
- **IAM Role**: `aws_iam_role.course_manager_rds_enhanced_monitoring_role`
- **Name**: `${var.project_name}-course-manager-rds-enhanced-monitoring-role`

## Container Registry

### ECR Repository
- **Resource**: `aws_ecr_repository.course_manager_ecr_repository`
- **Name**: `${var.project_name}-course-manager-repository`
- **Description**: Docker image repository for Course Manager

### ECR Lifecycle Policy
- **Resource**: `aws_ecr_lifecycle_policy.course_manager_ecr_lifecycle_policy`

### ECR Repository Policy
- **Resource**: `aws_ecr_repository_policy.course_manager_ecr_repository_policy`

## ECS Resources

### ECS Cluster
- **Resource**: `aws_ecs_cluster.course_manager_cluster`
- **Name**: `${var.project_name}-ecs-cluster`
- **Description**: ECS cluster for Course Manager

### ECS Capacity Providers
- **Resource**: `aws_ecs_cluster_capacity_providers.course_manager_capacity_providers`

### ECS Task Definition
- **Resource**: `aws_ecs_task_definition.course_manager_task_definition`
- **Family**: `${var.project_name}-course-manager-task`
- **Container Name**: `${var.project_name}-course-manager-container`

### ECS Service
- **Resource**: `aws_ecs_service.course_manager_ecs_service`
- **Name**: `${var.project_name}-course-manager-ecs-service`

### ECS Security Group
- **Resource**: `aws_security_group.course_manager_ecs_tasks`
- **Name**: `${var.project_name}-course-manager-ecs-tasks-sg`

### ECS IAM Roles
- **Task Execution Role**: `aws_iam_role.course_manager_ecs_task_execution_role`
- **Name**: `${var.project_name}-course-manager-ecs-task-execution-role`
- **Task Role**: `aws_iam_role.course_manager_ecs_task_role`
- **Name**: `${var.project_name}-course-manager-ecs-task-role`

### ECS IAM Policies
- **Secrets Policy**: `aws_iam_role_policy.course_manager_ecs_task_execution_secrets_policy`
- **Name**: `${var.project_name}-course-manager-ecs-task-execution-secrets-policy`
- **Exec Policy**: `aws_iam_role_policy.course_manager_ecs_task_exec_policy`
- **Name**: `${var.project_name}-course-manager-ecs-task-exec-policy`

## Load Balancer Resources

### Application Load Balancer
- **Resource**: `aws_lb.course_manager_alb`
- **Name**: `${var.project_name}-course-manager-alb`
- **Description**: Application Load Balancer for Course Manager

### ALB Security Group
- **Resource**: `aws_security_group.course_manager_alb`
- **Name**: `${var.project_name}-course-manager-alb-security-group`

### Target Group
- **Resource**: `aws_lb_target_group.course_manager_target_group`
- **Name**: `${var.project_name}-course-manager-target-group`

### ALB Listeners
- **HTTP Listener**: `aws_lb_listener.course_manager_http_listener`
- **HTTPS Listener**: `aws_lb_listener.course_manager_https_listener`
- **HTTP Only Listener**: `aws_lb_listener.course_manager_http_only_listener`

### ALB Access Logs
- **S3 Bucket**: `aws_s3_bucket.course_manager_alb_logs`
- **Name**: `${var.project_name}-course-manager-alb-logs-{random_suffix}`
- **Bucket Policy**: `aws_s3_bucket_policy.course_manager_alb_logs_policy`
- **Versioning**: `aws_s3_bucket_versioning.course_manager_alb_logs_versioning`
- **Encryption**: `aws_s3_bucket_server_side_encryption_configuration.course_manager_alb_logs_encryption`
- **Lifecycle**: `aws_s3_bucket_lifecycle_configuration.course_manager_alb_logs_lifecycle`

## Auto Scaling Resources

### Auto Scaling Target
- **Resource**: `aws_appautoscaling_target.course_manager_autoscaling_target`
- **Name**: `${var.project_name}-course-manager-autoscaling-target`

### Auto Scaling Policies
- **CPU Policy**: `aws_appautoscaling_policy.course_manager_cpu_autoscaling_policy`
- **Name**: `${var.project_name}-course-manager-cpu-autoscaling-policy`
- **Memory Policy**: `aws_appautoscaling_policy.course_manager_memory_autoscaling_policy`
- **Name**: `${var.project_name}-course-manager-memory-autoscaling-policy`
- **Requests Policy**: `aws_appautoscaling_policy.course_manager_requests_autoscaling_policy`
- **Name**: `${var.project_name}-course-manager-requests-autoscaling-policy`

## Monitoring Resources

### CloudWatch Log Groups
- **Application Logs**: `aws_cloudwatch_log_group.course_manager_app_logs`
- **Name**: `/aws/ecs/course-manager/${var.project_name}`
- **ECS Exec Logs**: `aws_cloudwatch_log_group.course_manager_ecs_exec_logs`
- **Name**: `/aws/ecs/course-manager/${var.project_name}/exec`

### CloudWatch Alarms
- **High CPU Alarm**: `aws_cloudwatch_metric_alarm.course_manager_high_cpu_alarm`
- **Name**: `${var.project_name}-course-manager-high-cpu-alarm`
- **High Memory Alarm**: `aws_cloudwatch_metric_alarm.course_manager_high_memory_alarm`
- **Name**: `${var.project_name}-course-manager-high-memory-alarm`

### SNS Topic
- **Resource**: `aws_sns_topic.course_manager_alerts_topic`
- **Name**: `${var.project_name}-course-manager-alerts`

## Utility Resources

### Random String
- **Resource**: `random_string.course_manager_bucket_suffix`
- **Description**: Used for unique S3 bucket naming

## Resource Naming Convention

All resources follow a consistent naming pattern:
- **Format**: `${var.project_name}-course-manager-{resource-type}-{optional-suffix}`
- **Example**: `gradeservice-course-manager-ecs-cluster`
- **Tags**: All resources include `Service = "course-manager"` tag

This naming convention ensures:
1. **Clear identification** of resources belonging to Course Manager
2. **Easy filtering** in AWS Console using tags or name patterns
3. **Consistent organization** across all infrastructure components
4. **Simplified management** and troubleshooting
