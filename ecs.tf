# ECS Cluster and Service for Course Manager

# ECS Cluster for Course Manager
resource "aws_ecs_cluster" "course_manager_cluster" {
  name = "${var.project_name}-ecs-cluster"

  configuration {
    execute_command_configuration {
      logging = "OVERRIDE"
      log_configuration {
        cloud_watch_log_group_name = aws_cloudwatch_log_group.course_manager_ecs_exec_logs.name
      }
    }
  }

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  tags = {
    Name        = "${var.project_name}-ecs-cluster"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# ECS Cluster Capacity Providers for Course Manager
resource "aws_ecs_cluster_capacity_providers" "course_manager_capacity_providers" {
  cluster_name = aws_ecs_cluster.course_manager_cluster.name

  capacity_providers = ["FARGATE", "FARGATE_SPOT"]

  default_capacity_provider_strategy {
    base              = 1
    weight            = 100
    capacity_provider = "FARGATE"
  }
}

# CloudWatch Log Group for Course Manager Application
resource "aws_cloudwatch_log_group" "course_manager_app_logs" {
  name              = "/aws/ecs/course-manager/${var.project_name}"
  retention_in_days = var.log_retention_days

  tags = {
    Name        = "${var.project_name}-application-logs"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# CloudWatch Log Group for Course Manager ECS Exec
resource "aws_cloudwatch_log_group" "course_manager_ecs_exec_logs" {
  name              = "/aws/ecs/course-manager/${var.project_name}/exec"
  retention_in_days = var.log_retention_days

  tags = {
    Name        = "${var.project_name}-ecs-exec-logs"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# Security Group for Course Manager ECS Tasks
resource "aws_security_group" "course_manager_ecs_tasks" {
  name_prefix = "${var.project_name}-course-manager-ecs-tasks-"
  vpc_id      = aws_vpc.course_manager_vpc.id

  ingress {
    description     = "HTTP from Course Manager ALB"
    from_port       = var.app_port
    to_port         = var.app_port
    protocol        = "tcp"
    security_groups = [aws_security_group.course_manager_alb.id]
  }

  egress {
    description = "All outbound traffic for Course Manager"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${var.project_name}-course-manager-ecs-tasks-sg"
    Environment = var.environment
    Service     = "course-manager"
  }

  lifecycle {
    create_before_destroy = true
  }
}

# IAM Role for Course Manager ECS Task Execution
resource "aws_iam_role" "course_manager_ecs_task_execution_role" {
  name = "${var.project_name}-course-manager-ecs-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name        = "${var.project_name}-course-manager-ecs-task-execution-role"
    Environment = var.environment
    Service     = "course-manager"
  }
}

resource "aws_iam_role_policy_attachment" "course_manager_ecs_task_execution_policy" {
  role       = aws_iam_role.course_manager_ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Additional policy for Course Manager Secrets Manager access
resource "aws_iam_role_policy" "course_manager_ecs_task_execution_secrets_policy" {
  name = "${var.project_name}-course-manager-ecs-task-execution-secrets-policy"
  role = aws_iam_role.course_manager_ecs_task_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue"
        ]
        Resource = [
          aws_secretsmanager_secret.course_manager_db_credentials.arn
        ]
      }
    ]
  })
}

# IAM Role for Course Manager ECS Task
resource "aws_iam_role" "course_manager_ecs_task_role" {
  name = "${var.project_name}-course-manager-ecs-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name        = "${var.project_name}-course-manager-ecs-task-role"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# Policy for Course Manager ECS Exec
resource "aws_iam_role_policy" "course_manager_ecs_task_exec_policy" {
  name = "${var.project_name}-course-manager-ecs-task-exec-policy"
  role = aws_iam_role.course_manager_ecs_task_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ssmmessages:CreateControlChannel",
          "ssmmessages:CreateDataChannel",
          "ssmmessages:OpenControlChannel",
          "ssmmessages:OpenDataChannel"
        ]
        Resource = "*"
      }
    ]
  })
}

# ECS Task Definition for Course Manager
resource "aws_ecs_task_definition" "course_manager_task_definition" {
  family                   = "${var.project_name}-course-manager-task"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.app_cpu
  memory                   = var.app_memory
  execution_role_arn       = aws_iam_role.course_manager_ecs_task_execution_role.arn
  task_role_arn           = aws_iam_role.course_manager_ecs_task_role.arn

  container_definitions = jsonencode([
    {
      name  = "${var.project_name}-course-manager-container"
      image = "${aws_ecr_repository.course_manager_ecr_repository.repository_url}:${var.image_tag}"

      portMappings = [
        {
          containerPort = var.app_port
          protocol      = "tcp"
        }
      ]

      environment = [
        {
          name  = "ENVIRONMENT"
          value = var.environment
        },
        {
          name  = "DB_HOST"
          value = aws_db_instance.course_manager_database.endpoint
        },
        {
          name  = "DB_PORT"
          value = tostring(aws_db_instance.course_manager_database.port)
        },
        {
          name  = "DB_NAME"
          value = var.db_name
        }
      ]

      secrets = [
        {
          name      = "DB_USERNAME"
          valueFrom = "${aws_secretsmanager_secret.course_manager_db_credentials.arn}:username::"
        },
        {
          name      = "DB_PASSWORD"
          valueFrom = "${aws_secretsmanager_secret.course_manager_db_credentials.arn}:password::"
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.course_manager_app_logs.name
          "awslogs-region"        = var.aws_region
          "awslogs-stream-prefix" = "course-manager"
        }
      }

      healthCheck = {
        command = [
          "CMD-SHELL",
          "curl -f http://localhost:${var.app_port}${var.health_check_path} || exit 1"
        ]
        interval    = 30
        timeout     = 5
        retries     = 3
        startPeriod = 60
      }

      essential = true
    }
  ])

  tags = {
    Name        = "${var.project_name}-course-manager-task-definition"
    Environment = var.environment
    Service     = "course-manager"
  }
}
