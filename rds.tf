# RDS MySQL Database for Course Manager

# DB Subnet Group for Course Manager Database
resource "aws_db_subnet_group" "course_manager_db_subnet_group" {
  name       = "${var.project_name}-course-manager-db-subnet-group"
  subnet_ids = aws_subnet.course_manager_private_subnets[*].id

  tags = {
    Name        = "${var.project_name}-course-manager-database-subnet-group"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# DB Parameter Group for Course Manager Database
resource "aws_db_parameter_group" "course_manager_db_parameter_group" {
  family = "mysql8.0"
  name   = "${var.project_name}-course-manager-db-parameter-group"

  parameter {
    name  = "innodb_buffer_pool_size"
    value = "{DBInstanceClassMemory*3/4}"
  }

  parameter {
    name  = "max_connections"
    value = "1000"
  }

  parameter {
    name  = "slow_query_log"
    value = "1"
  }

  parameter {
    name  = "long_query_time"
    value = "2"
  }

  tags = {
    Name        = "${var.project_name}-course-manager-database-parameter-group"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# Security Group for Course Manager RDS Database
resource "aws_security_group" "course_manager_database_security_group" {
  name_prefix = "${var.project_name}-course-manager-database-"
  vpc_id      = aws_vpc.course_manager_vpc.id

  ingress {
    description     = "MySQL from Course Manager ECS Tasks"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.course_manager_ecs_tasks.id]
  }

  egress {
    description = "All outbound traffic from Course Manager Database"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${var.project_name}-course-manager-database-security-group"
    Environment = var.environment
    Service     = "course-manager"
  }

  lifecycle {
    create_before_destroy = true
  }
}

# RDS Instance for Course Manager Database
resource "aws_db_instance" "course_manager_database" {
  identifier = "${var.project_name}-course-manager-database"

  # Engine
  engine         = "mysql"
  engine_version = "8.0"

  # Instance
  instance_class        = var.db_instance_class
  allocated_storage     = var.db_allocated_storage
  max_allocated_storage = var.db_max_allocated_storage
  storage_type          = "gp3"
  storage_encrypted     = true

  # Database
  db_name  = var.db_name
  username = var.db_username
  password = var.db_password

  # Network
  db_subnet_group_name   = aws_db_subnet_group.course_manager_db_subnet_group.name
  vpc_security_group_ids = [aws_security_group.course_manager_database_security_group.id]
  publicly_accessible    = false

  # Backup
  backup_retention_period = var.backup_retention_period
  backup_window          = var.backup_window
  maintenance_window     = var.maintenance_window
  delete_automated_backups = false

  # Monitoring
  monitoring_interval = 60
  monitoring_role_arn = aws_iam_role.course_manager_rds_enhanced_monitoring_role.arn

  # Performance Insights
  performance_insights_enabled = true
  performance_insights_retention_period = 7

  # Parameter Group
  parameter_group_name = aws_db_parameter_group.course_manager_db_parameter_group.name

  # Deletion Protection
  deletion_protection = true
  skip_final_snapshot = false
  final_snapshot_identifier = "${var.project_name}-course-manager-database-final-snapshot-${formatdate("YYYY-MM-DD-hhmm", timestamp())}"

  tags = {
    Name        = "${var.project_name}-course-manager-database"
    Environment = var.environment
    Service     = "course-manager"
  }

  lifecycle {
    ignore_changes = [
      password,
      final_snapshot_identifier
    ]
  }
}

# IAM Role for Course Manager RDS Enhanced Monitoring
resource "aws_iam_role" "course_manager_rds_enhanced_monitoring_role" {
  name = "${var.project_name}-course-manager-rds-enhanced-monitoring-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "monitoring.rds.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name        = "${var.project_name}-course-manager-rds-enhanced-monitoring-role"
    Environment = var.environment
    Service     = "course-manager"
  }
}

resource "aws_iam_role_policy_attachment" "course_manager_rds_enhanced_monitoring_policy_attachment" {
  role       = aws_iam_role.course_manager_rds_enhanced_monitoring_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"
}

# Secrets Manager for Course Manager Database Credentials
resource "aws_secretsmanager_secret" "course_manager_db_credentials" {
  name        = "${var.project_name}/course-manager/database/credentials"
  description = "Database credentials for Course Manager ${var.project_name}"

  tags = {
    Name        = "${var.project_name}-course-manager-database-credentials"
    Environment = var.environment
    Service     = "course-manager"
  }
}

resource "aws_secretsmanager_secret_version" "course_manager_db_credentials_version" {
  secret_id = aws_secretsmanager_secret.course_manager_db_credentials.id
  secret_string = jsonencode({
    username = var.db_username
    password = var.db_password
    engine   = "mysql"
    host     = aws_db_instance.course_manager_database.endpoint
    port     = aws_db_instance.course_manager_database.port
    dbname   = var.db_name
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}
