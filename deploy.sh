#!/bin/bash

# Course Manager ECS Deployment Script
# This script automates the deployment process

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="gradeservice"
AWS_REGION="us-west-2"
IMAGE_TAG="${1:-latest}"

echo -e "${BLUE}🚀 Course Manager ECS Deployment${NC}"
echo -e "${BLUE}===================================${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

# Check if terraform is installed
if ! command -v terraform &> /dev/null; then
    print_error "Terraform is not installed. Please install Terraform first."
    exit 1
fi
print_status "Terraform is installed"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install AWS CLI first."
    exit 1
fi
print_status "AWS CLI is installed"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi
print_status "Docker is installed"

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS credentials not configured. Please run 'aws configure' first."
    exit 1
fi
print_status "AWS credentials are configured"

# Check if terraform.tfvars exists
if [ ! -f "terraform.tfvars" ]; then
    print_warning "terraform.tfvars not found. Creating from example..."
    cp terraform.tfvars.example terraform.tfvars
    print_error "Please edit terraform.tfvars with your specific values, especially the database password!"
    echo "Then run this script again."
    exit 1
fi
print_status "terraform.tfvars found"

echo ""

# Initialize Terraform
echo -e "${BLUE}🔧 Initializing Terraform...${NC}"
terraform init
print_status "Terraform initialized"

echo ""

# Plan deployment
echo -e "${BLUE}📋 Planning deployment...${NC}"
terraform plan -out=tfplan
print_status "Terraform plan created"

echo ""

# Ask for confirmation
read -p "Do you want to proceed with the deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Deployment cancelled"
    exit 0
fi

echo ""

# Apply Terraform
echo -e "${BLUE}🚀 Deploying infrastructure...${NC}"
terraform apply tfplan
print_status "Infrastructure deployed"

echo ""

# Get ECR repository URL
echo -e "${BLUE}📦 Getting ECR repository information...${NC}"
ECR_REPO=$(terraform output -raw ecr_repository_url)
print_status "ECR repository: $ECR_REPO"

echo ""

# Build and push Docker image
echo -e "${BLUE}🐳 Building and pushing Docker image...${NC}"

# Check if Dockerfile exists in parent directory
if [ ! -f "../Dockerfile" ]; then
    print_error "Dockerfile not found in parent directory. Please ensure your application has a Dockerfile."
    exit 1
fi

# Login to ECR
print_status "Logging into ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REPO

# Build Docker image
print_status "Building Docker image..."
cd ..
docker build -t $PROJECT_NAME:$IMAGE_TAG .
cd terraform

# Tag image for ECR
print_status "Tagging image for ECR..."
docker tag $PROJECT_NAME:$IMAGE_TAG $ECR_REPO:$IMAGE_TAG

# Push image to ECR
print_status "Pushing image to ECR..."
docker push $ECR_REPO:$IMAGE_TAG

echo ""

# Update ECS service
echo -e "${BLUE}🔄 Updating ECS service...${NC}"
CLUSTER_NAME=$(terraform output -raw ecs_cluster_name)
SERVICE_NAME=$(terraform output -raw ecs_service_name)

aws ecs update-service \
    --cluster $CLUSTER_NAME \
    --service $SERVICE_NAME \
    --force-new-deployment \
    --region $AWS_REGION

print_status "ECS service update initiated"

echo ""

# Wait for deployment to complete
echo -e "${BLUE}⏳ Waiting for deployment to complete...${NC}"
aws ecs wait services-stable \
    --cluster $CLUSTER_NAME \
    --services $SERVICE_NAME \
    --region $AWS_REGION

print_status "Deployment completed successfully"

echo ""

# Display deployment information
echo -e "${BLUE}📊 Deployment Summary${NC}"
echo -e "${BLUE}===================${NC}"
echo ""

APP_URL=$(terraform output -raw application_url)
ALB_DNS=$(terraform output -raw load_balancer_dns_name)
DB_ENDPOINT=$(terraform output -raw rds_endpoint)

echo -e "${GREEN}🌐 Course Manager Application URL:${NC} $APP_URL"
echo -e "${GREEN}🔗 Course Manager Load Balancer DNS:${NC} $ALB_DNS"
echo -e "${GREEN}🗄️  Course Manager Database Endpoint:${NC} $DB_ENDPOINT"
echo -e "${GREEN}📦 Course Manager ECR Repository:${NC} $ECR_REPO"
echo -e "${GREEN}🏗️  Course Manager ECS Cluster:${NC} $CLUSTER_NAME"
echo -e "${GREEN}⚙️  Course Manager ECS Service:${NC} $SERVICE_NAME"

echo ""
echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo ""
echo -e "${YELLOW}📝 Next Steps:${NC}"
echo "1. Wait a few minutes for the application to fully start"
echo "2. Access your application at: $APP_URL"
echo "3. Monitor logs: aws logs tail /ecs/$PROJECT_NAME --follow"
echo "4. Check service health in AWS Console"

echo ""
echo -e "${BLUE}🔧 Useful Commands:${NC}"
echo "• View logs: aws logs tail /ecs/$PROJECT_NAME --follow"
echo "• Check service: aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME"
echo "• Scale service: aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --desired-count <number>"
echo "• Destroy infrastructure: terraform destroy"

echo ""
print_status "Deployment script completed!"
